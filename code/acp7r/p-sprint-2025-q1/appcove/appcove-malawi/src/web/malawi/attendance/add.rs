#[approck::http(GET /malawi/attendance/add/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
    ) -> Result<Response> {
        use crate::web::malawi::guard::index::guard_list;
        use maud::html;

        // Get list of all active guards
        let guards = guard_list::call(
            app,
            identity,
            guard_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Format guards for dropdown: "Name (ESID)"
        // Convert to Vec<(&str, &str)> for nilla_select
        let guard_options_strings: Vec<(String, String)> = guards
            .guard_list
            .iter()
            .map(|g| {
                let display = format!("{}", g.name);
                (g.guard_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let guard_options: Vec<(&str, &str)> = guard_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        doc.set_title("Add Attendance");

        let mut form_panel =
            bux::component::add_cancel_form_panel("Add Attendance", "/attendance/list");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::select::nilla::nilla_select("guard_uuid", "Guard", &guard_options, None))
            (bux::input::datetime::bux_input_datetime("start_ts", "Start Time", None))
            (bux::input::datetime::bux_input_datetime("end_ts", "End Time", None))
            (bux::input::textarea::string::name_label_value("note", "Note", None))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod attendance_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_uuid: Uuid,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_attendance_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.attendance_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to add attendance".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $guard_uuid: &input.guard_uuid,
                    $start_ts: &input.start_ts,
                    $end_ts: &input.end_ts,
                    $note: &input.note,
                };
                row = {
                    guard_attendance_uuid: Uuid,
                };

                INSERT INTO
                    appcove_malawi.guard_attendance
                    (
                        guard_uuid,
                        start_ts,
                        end_ts,
                        note
                    )
                VALUES
                    (
                        $guard_uuid,
                        $start_ts,
                        $end_ts,
                        $note
                    )
                RETURNING
                    guard_attendance_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            guard_attendance_uuid: row.guard_attendance_uuid,
            detail_url: crate::ml_guard_attendance(row.guard_attendance_uuid),
        }))
    }
}
