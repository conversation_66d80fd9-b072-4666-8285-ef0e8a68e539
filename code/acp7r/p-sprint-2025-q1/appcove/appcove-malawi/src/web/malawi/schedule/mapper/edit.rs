#[approck::http(GET /malawi/schedule/{guard_schedule_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Edit Schedule");

        let mut panel = bux::component::save_cancel_form_panel(
            "Edit Schedule",
            "/malawi/schedule/00000000-0000-0000-0000-000000000000/",
        );
        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::textarea::string::name_label_value("note", "Note", None))
            (bux::input::datetime::bux_input_datetime("start_ts", "Start Time", None))
            (bux::input::datetime::bux_input_datetime("end_ts", "End Time", None))
            (bux::input::text::string::name_label_value("guard_uuid", "Guard", None))
            (bux::input::text::string::name_label_value("guard_name", "Guard Name", None))
            (bux::input::checkbox::name_label_checked("confirm", "Confirm", false))
        ));
        doc.add_body(html! {
            bux-action-panel {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
#[approck::api]
pub mod schedule_edit_confirm {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_schedule_uuid: Uuid,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_schedule_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.schedule_edit_confirm(input.guard_schedule_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to editschedule".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $guard_schedule_uuid: &input.guard_schedule_uuid,
                    $start_ts: &input.start_ts,
                    $end_ts: &input.end_ts,
                    $note: &input.note,
                };
                row = {
                    guard_schedule_uuid: Uuid,
                };

                INSERT INTO
                    appcove_malawi.guard_schedule
                    (
                        guard_uuid,
                        start_ts,
                        end_ts,
                        note
                    )
                VALUES
                    (
                        $guard_schedule_uuid,
                        $start_ts,
                        $end_ts,
                        $note
                    )
                RETURNING
                    guard_schedule_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            guard_schedule_uuid: row.guard_schedule_uuid,
            detail_url: crate::ml_guard_schedule(row.guard_schedule_uuid),
        }))
    }
}
