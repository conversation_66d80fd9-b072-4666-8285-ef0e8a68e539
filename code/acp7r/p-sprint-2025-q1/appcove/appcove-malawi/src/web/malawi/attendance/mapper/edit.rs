#[approck::http(GET /malawi/attendance/{guard_attendance_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use maud::html;

        doc.set_title("Edit attendance");

        let mut panel = bux::component::save_cancel_form_panel(
            "Edit attendance",
            "/malawi/attendance/00000000-0000-0000-0000-000000000000/",
        );
        panel.set_hidden("guard_attendance_uuid", path.guard_attendance_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::textarea::string::name_label_value("note", "Note", None))
            (bux::input::datetime::bux_input_datetime("start_ts", "Start Time", None))
            (bux::input::datetime::bux_input_datetime("end_ts", "End Time", None))
            (bux::input::checkbox::name_label_checked("confirm", "Confirm", false))
        ));
        doc.add_body(html! {
            bux-action-panel {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
#[approck::api]
pub mod attendance_edit_confirm {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_attendance_uuid: Uuid,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_attendance_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.attendance_edit_confirm(input.guard_attendance_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit attendance".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $guard_attendance_uuid: &input.guard_attendance_uuid,
                    $note: &input.note,
                };
                row = {
                    guard_attendance_uuid: Uuid,
                };

                INSERT INTO
                    appcove_malawi.guard_attendance
                    (
                        guard_attendance_uuid,
                        note
                    )
                VALUES
                    (
                        $guard_attendance_uuid,
                        $note
                    )
                RETURNING
                    guard_attendance_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            guard_attendance_uuid: row.guard_attendance_uuid,
            detail_url: crate::ml_guard_attendance(row.guard_attendance_uuid),
        }))
    }
}
