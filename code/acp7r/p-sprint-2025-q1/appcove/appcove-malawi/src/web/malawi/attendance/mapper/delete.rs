#[approck::http(GET /malawi/attendance/{guard_attendance_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::attendance::mapper::index::guard_attendance_detail;
        use maud::html;

        // Get attendance details
        let attendance = guard_attendance_detail::call(
            app,
            identity,
            guard_attendance_detail::Input {
                guard_attendance_uuid: path.guard_attendance_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Attendance");

        let title = format!(
            "Delete Attendance for {}?",
            attendance.guard_name
        );

        let mut panel = bux::component::delete_cancel_form_panel(
            &title,
            &crate::ml_attendance_list(),
        );
        panel.set_hidden("guard_attendance_uuid", path.guard_attendance_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the attendance for "
                strong { (attendance.guard_name) }
                " from " (attendance.start_ts.format("%Y-%m-%d %H:%M:%S"))
                " to " (attendance.end_ts.format("%Y-%m-%d %H:%M:%S")) ". "
                "The attendance will no longer appear in active attendance lists, but all historical data will be preserved."
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this attendance.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod attendance_delete {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_attendance_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.attendance_delete(input.guard_attendance_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete attendance".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $guard_attendance_uuid: &input.guard_attendance_uuid,
            };
            UPDATE
                appcove_malawi.guard_attendance
            SET
                active = false
            WHERE
                guard_attendance_uuid = $guard_attendance_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_attendance_list(),
            message: "Attendance deleted".into(),
        }))
    }
}